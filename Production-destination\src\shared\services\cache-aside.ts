/**
 * Generic Cache-Aside Service
 * Provides a reusable pattern for cache-aside operations with database fallback
 * Ensures data consistency and graceful degradation when Redis is unavailable
 */

import { redis } from './redis';
import { db } from './database';
import { logger } from '../utils/logger';

export interface CacheAsideOptions {
  ttlSeconds?: number;
  enableFallback?: boolean;
  cachePrefix?: string;
  invalidatePatterns?: string[];
}

export interface DatabaseQuery {
  containerName: string;
  query?: string;
  parameters?: any[];
  itemId?: string;
  partitionKey?: string;
}

export class CacheAsideService {
  private static instance: CacheAsideService;

  private constructor() {}

  public static getInstance(): CacheAsideService {
    if (!CacheAsideService.instance) {
      CacheAsideService.instance = new CacheAsideService();
    }
    return CacheAsideService.instance;
  }

  /**
   * Generic get operation with cache-aside pattern
   */
  public async get<T>(
    cacheKey: string,
    dbQuery: DatabaseQuery,
    options: CacheAsideOptions = {}
  ): Promise<T | null> {
    const {
      ttlSeconds = 3600,
      enableFallback = true,
      cachePrefix = ''
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      // Try Redis first
      const cachedData = await redis.getJson<T>(fullCacheKey);
      if (cachedData) {
        logger.debug('Data found in Redis cache', { cacheKey: fullCacheKey });
        return cachedData;
      }

      // Fallback to database if enabled
      if (enableFallback) {
        const dbData = await this.queryDatabase<T>(dbQuery);
        
        if (dbData) {
          // Cache the data back to Redis for future requests
          await redis.setJson(fullCacheKey, dbData, ttlSeconds);
          logger.info('Data retrieved from database and cached', { cacheKey: fullCacheKey });
          return dbData;
        }
      }

      logger.debug('Data not found in cache or database', { cacheKey: fullCacheKey });
      return null;

    } catch (error) {
      logger.error('Cache-aside get operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      
      // If Redis fails, try database directly
      if (enableFallback) {
        try {
          return await this.queryDatabase<T>(dbQuery);
        } catch (dbError) {
          logger.error('Database fallback also failed', {
            error: dbError instanceof Error ? dbError.message : String(dbError),
            cacheKey: fullCacheKey
          });
        }
      }
      
      return null;
    }
  }

  /**
   * Generic set operation with cache invalidation
   */
  public async set<T>(
    cacheKey: string,
    data: T,
    options: CacheAsideOptions = {}
  ): Promise<boolean> {
    const {
      ttlSeconds = 3600,
      cachePrefix = '',
      invalidatePatterns = []
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      // Set data in Redis
      const success = await redis.setJson(fullCacheKey, data, ttlSeconds);
      
      if (success) {
        // Invalidate related caches
        for (const pattern of invalidatePatterns) {
          await this.invalidatePattern(pattern);
        }
        
        logger.debug('Data cached and related caches invalidated', { 
          cacheKey: fullCacheKey,
          invalidatedPatterns: invalidatePatterns
        });
      }
      
      return success;

    } catch (error) {
      logger.error('Cache-aside set operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      return false;
    }
  }

  /**
   * Get list of items with cache-aside pattern
   */
  public async getList<T>(
    cacheKey: string,
    dbQuery: DatabaseQuery,
    options: CacheAsideOptions = {}
  ): Promise<T[]> {
    const {
      ttlSeconds = 3600,
      enableFallback = true,
      cachePrefix = ''
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      // Try Redis first
      const cachedList = await redis.getJson<T[]>(fullCacheKey);
      if (cachedList && Array.isArray(cachedList)) {
        logger.debug('List found in Redis cache', { cacheKey: fullCacheKey, count: cachedList.length });
        return cachedList;
      }

      // Fallback to database if enabled
      if (enableFallback) {
        const dbList = await this.queryDatabaseList<T>(dbQuery);
        
        if (dbList && dbList.length > 0) {
          // Cache the list back to Redis for future requests
          await redis.setJson(fullCacheKey, dbList, ttlSeconds);
          logger.info('List retrieved from database and cached', { 
            cacheKey: fullCacheKey, 
            count: dbList.length 
          });
          return dbList;
        }
      }

      logger.debug('List not found in cache or database', { cacheKey: fullCacheKey });
      return [];

    } catch (error) {
      logger.error('Cache-aside getList operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      
      // If Redis fails, try database directly
      if (enableFallback) {
        try {
          return await this.queryDatabaseList<T>(dbQuery);
        } catch (dbError) {
          logger.error('Database fallback also failed for list', {
            error: dbError instanceof Error ? dbError.message : String(dbError),
            cacheKey: fullCacheKey
          });
        }
      }
      
      return [];
    }
  }

  /**
   * Delete from cache and invalidate related patterns
   */
  public async delete(
    cacheKey: string,
    options: CacheAsideOptions = {}
  ): Promise<boolean> {
    const {
      cachePrefix = '',
      invalidatePatterns = []
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      const success = await redis.delete(fullCacheKey);
      
      // Invalidate related caches
      for (const pattern of invalidatePatterns) {
        await this.invalidatePattern(pattern);
      }
      
      logger.debug('Cache deleted and related caches invalidated', { 
        cacheKey: fullCacheKey,
        invalidatedPatterns: invalidatePatterns
      });
      
      return success;

    } catch (error) {
      logger.error('Cache-aside delete operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      return false;
    }
  }

  /**
   * Query database for single item
   */
  private async queryDatabase<T>(dbQuery: DatabaseQuery): Promise<T | null> {
    try {
      if (dbQuery.itemId && dbQuery.partitionKey) {
        // Direct item read
        return await db.readItem<T>(dbQuery.containerName, dbQuery.itemId, dbQuery.partitionKey);
      } else if (dbQuery.query && dbQuery.parameters) {
        // Query with parameters
        const results = await db.queryItems(dbQuery.containerName, dbQuery.query, dbQuery.parameters);
        return results.length > 0 ? results[0] as T : null;
      } else {
        throw new Error('Invalid database query configuration');
      }
    } catch (error) {
      logger.error('Database query failed', {
        error: error instanceof Error ? error.message : String(error),
        containerName: dbQuery.containerName
      });
      throw error;
    }
  }

  /**
   * Query database for list of items
   */
  private async queryDatabaseList<T>(dbQuery: DatabaseQuery): Promise<T[]> {
    try {
      if (dbQuery.query && dbQuery.parameters) {
        const results = await db.queryItems(dbQuery.containerName, dbQuery.query, dbQuery.parameters);
        return results as T[];
      } else {
        throw new Error('Invalid database query configuration for list');
      }
    } catch (error) {
      logger.error('Database list query failed', {
        error: error instanceof Error ? error.message : String(error),
        containerName: dbQuery.containerName
      });
      throw error;
    }
  }

  /**
   * Invalidate cache pattern
   */
  private async invalidatePattern(pattern: string): Promise<void> {
    try {
      if (redis.isAvailable()) {
        // Use Redis service's deleteByPattern method
        await (redis as any).deleteByPattern(pattern);
      }
    } catch (error) {
      logger.error('Failed to invalidate cache pattern', {
        error: error instanceof Error ? error.message : String(error),
        pattern
      });
    }
  }
}

// Export singleton instance
export const cacheAside = CacheAsideService.getInstance();
export default cacheAside;
